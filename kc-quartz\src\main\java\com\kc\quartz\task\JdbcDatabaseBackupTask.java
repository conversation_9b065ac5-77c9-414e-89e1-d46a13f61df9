package com.kc.quartz.task;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.io.*;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 基于JDBC的数据库备份定时任务
 * 
 * <AUTHOR>
 */
@Component("jdbcDatabaseBackupTask")
public class JdbcDatabaseBackupTask {
    
    private static final Logger log = LoggerFactory.getLogger(JdbcDatabaseBackupTask.class);
    
    @Autowired
    private DataSource dataSource;
    
    // 备份文件存储路径
    @Value("${database.backup.path:/home/<USER>/database}")
    private String backupPath;
    
    /**
     * 执行数据库备份
     */
    public void executeBackup() {
        log.info("开始执行JDBC数据库备份任务...");
        
        try {
            // 获取数据库信息
            String databaseName = getDatabaseName();
            
            // 创建备份目录
            createBackupDirectory();
            
            // 生成备份文件名
            String backupFileName = generateBackupFileName(databaseName);
            String backupFilePath = backupPath + File.separator + backupFileName;
            
            // 执行JDBC备份
            boolean success = performJdbcBackup(databaseName, backupFilePath);
            
            if (success) {
                double fileSize = getFileSize(backupFilePath);
                log.info("数据库备份成功完成，备份文件：{}，大小：{:.2f} MB", backupFilePath, fileSize);
                
                // 清理旧的备份文件
                cleanOldBackups();
            } else {
                log.error("数据库备份失败");
            }
            
        } catch (Exception e) {
            log.error("数据库备份任务执行异常：", e);
        }
    }
    
    /**
     * 获取数据库名称
     */
    private String getDatabaseName() throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            String url = metaData.getURL();
            // 从URL中提取数据库名称
            if (url.contains("?")) {
                url = url.substring(0, url.indexOf("?"));
            }
            return url.substring(url.lastIndexOf("/") + 1);
        }
    }
    
    /**
     * 创建备份目录
     */
    private void createBackupDirectory() {
        File directory = new File(backupPath);
        if (!directory.exists()) {
            boolean created = directory.mkdirs();
            if (created) {
                log.info("创建备份目录：{}", backupPath);
            } else {
                log.warn("创建备份目录失败：{}", backupPath);
            }
        }
    }
    
    /**
     * 生成备份文件名
     */
    private String generateBackupFileName(String databaseName) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String timestamp = sdf.format(new Date());
        return String.format("%s_backup_%s.sql", databaseName, timestamp);
    }
    
    /**
     * 执行JDBC备份
     */
    private boolean performJdbcBackup(String databaseName, String backupFilePath) {
        try (Connection connection = dataSource.getConnection();
             OutputStreamWriter fileWriter = new OutputStreamWriter(
                 new FileOutputStream(backupFilePath), "UTF-8");
             PrintWriter writer = new PrintWriter(fileWriter)) {
            
            log.info("开始使用JDBC方式备份数据库：{}", databaseName);
            
            // 写入SQL文件头部信息
            writeSqlHeader(writer, databaseName);
            
            // 获取所有表名
            List<String> tableNames = getAllTableNames(connection, databaseName);
            log.info("发现 {} 个表需要备份", tableNames.size());
            
            // 备份每个表
            for (String tableName : tableNames) {
                log.info("正在备份表：{}", tableName);
                
                // 备份表结构
                backupTableStructure(connection, writer, tableName);
                
                // 备份表数据
                backupTableData(connection, writer, tableName);
                
                writer.println();
            }
            
            // 写入SQL文件尾部信息
            writeSqlFooter(writer);
            
            log.info("JDBC备份执行成功");
            return true;
            
        } catch (Exception e) {
            log.error("JDBC备份执行异常：", e);
            return false;
        }
    }
    
    /**
     * 写入SQL文件头部信息
     */
    private void writeSqlHeader(PrintWriter writer, String databaseName) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String currentTime = sdf.format(new Date());
        
        writer.println("-- MySQL Database Backup");
        writer.println("-- Database: " + databaseName);
        writer.println("-- Backup Time: " + currentTime);
        writer.println("-- Generated by ProjectAllocation JDBC Backup Task");
        writer.println();
        writer.println("SET FOREIGN_KEY_CHECKS=0;");
        writer.println("SET SQL_MODE='NO_AUTO_VALUE_ON_ZERO';");
        writer.println("SET AUTOCOMMIT=0;");
        writer.println("START TRANSACTION;");
        writer.println();
    }
    
    /**
     * 写入SQL文件尾部信息
     */
    private void writeSqlFooter(PrintWriter writer) {
        writer.println();
        writer.println("COMMIT;");
        writer.println("SET FOREIGN_KEY_CHECKS=1;");
        writer.println("SET AUTOCOMMIT=1;");
        writer.println();
        writer.println("-- Backup completed successfully");
    }
    
    /**
     * 获取所有表名
     */
    private List<String> getAllTableNames(Connection connection, String databaseName) throws SQLException {
        List<String> tableNames = new ArrayList<>();
        
        String sql = "SELECT table_name FROM information_schema.tables WHERE table_schema = ? AND table_type = 'BASE TABLE' ORDER BY table_name";
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, databaseName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    tableNames.add(rs.getString("table_name"));
                }
            }
        }
        
        return tableNames;
    }
    
    /**
     * 备份表结构
     */
    private void backupTableStructure(Connection connection, PrintWriter writer, String tableName) throws SQLException {
        String sql = "SHOW CREATE TABLE " + tableName;
        
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            if (rs.next()) {
                writer.println("-- ");
                writer.println("-- Table structure for table `" + tableName + "`");
                writer.println("-- ");
                writer.println();
                writer.println("DROP TABLE IF EXISTS `" + tableName + "`;");
                writer.println(rs.getString(2) + ";");
                writer.println();
            }
        }
    }
    
    /**
     * 备份表数据
     */
    private void backupTableData(Connection connection, PrintWriter writer, String tableName) throws SQLException {
        // 获取表的列信息
        List<String> columnNames = getTableColumns(connection, tableName);
        
        if (columnNames.isEmpty()) {
            return;
        }
        
        String sql = "SELECT * FROM " + tableName;
        
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            writer.println("-- ");
            writer.println("-- Dumping data for table `" + tableName + "`");
            writer.println("-- ");
            writer.println();
            
            boolean hasData = false;
            StringBuilder insertSql = new StringBuilder();
            
            while (rs.next()) {
                if (!hasData) {
                    writer.println("INSERT INTO `" + tableName + "` VALUES");
                    hasData = true;
                } else {
                    writer.println(",");
                }
                
                insertSql.setLength(0);
                insertSql.append("(");
                
                for (int i = 0; i < columnNames.size(); i++) {
                    if (i > 0) {
                        insertSql.append(", ");
                    }
                    
                    Object value = rs.getObject(i + 1);
                    if (value == null) {
                        insertSql.append("NULL");
                    } else if (value instanceof String || value instanceof Date || value instanceof Timestamp) {
                        insertSql.append("'").append(value.toString().replace("'", "\\'")).append("'");
                    } else {
                        insertSql.append(value.toString());
                    }
                }
                
                insertSql.append(")");
                writer.print(insertSql.toString());
            }
            
            if (hasData) {
                writer.println(";");
            }
            
            writer.println();
        }
    }
    
    /**
     * 获取表的列名
     */
    private List<String> getTableColumns(Connection connection, String tableName) throws SQLException {
        List<String> columnNames = new ArrayList<>();
        
        String sql = "SELECT column_name FROM information_schema.columns WHERE table_schema = ? AND table_name = ? ORDER BY ordinal_position";
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, getDatabaseName());
            stmt.setString(2, tableName);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    columnNames.add(rs.getString("column_name"));
                }
            }
        }
        
        return columnNames;
    }
    
    /**
     * 获取备份文件大小（MB）
     */
    private double getFileSize(String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            return file.length() / (1024.0 * 1024.0);
        }
        return 0;
    }
    
    /**
     * 清理旧的备份文件（保留最近5个备份）
     */
    private void cleanOldBackups() {
        try {
            File backupDir = new File(backupPath);
            File[] backupFiles = backupDir.listFiles((dir, name) -> name.endsWith(".sql"));
            
            if (backupFiles != null && backupFiles.length > 5) {
                // 按修改时间排序
                java.util.Arrays.sort(backupFiles, (f1, f2) -> 
                    Long.compare(f2.lastModified(), f1.lastModified()));
                
                // 删除超过5个的旧备份文件
                for (int i = 5; i < backupFiles.length; i++) {
                    if (backupFiles[i].delete()) {
                        log.info("删除旧备份文件：{}", backupFiles[i].getName());
                    }
                }
            }
        } catch (Exception e) {
            log.warn("清理旧备份文件时发生异常：", e);
        }
    }
    
    /**
     * 测试方法
     */
    public void testBackup() {
        log.info("执行JDBC测试备份...");
        executeBackup();
    }
}
