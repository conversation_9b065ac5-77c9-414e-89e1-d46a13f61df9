package com.kc.quartz.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 数据库备份管理服务
 * 
 * <AUTHOR>
 */
@Service
public class DatabaseBackupService {
    
    private static final Logger log = LoggerFactory.getLogger(DatabaseBackupService.class);
    
    @Value("${database.backup.path:/home/<USER>/database}")
    private String backupPath;
    
    /**
     * 获取备份文件列表
     */
    public List<Map<String, Object>> getBackupFileList() {
        List<Map<String, Object>> backupFiles = new ArrayList<>();
        
        try {
            File backupDir = new File(backupPath);
            if (!backupDir.exists()) {
                log.warn("备份目录不存在：{}", backupPath);
                return backupFiles;
            }
            
            File[] files = backupDir.listFiles((dir, name) -> 
                name.endsWith(".sql") || name.endsWith(".sql.gz"));
            
            if (files != null) {
                // 按修改时间倒序排列
                Arrays.sort(files, (f1, f2) -> 
                    Long.compare(f2.lastModified(), f1.lastModified()));
                
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                
                for (File file : files) {
                    Map<String, Object> fileInfo = new HashMap<>();
                    fileInfo.put("fileName", file.getName());
                    fileInfo.put("filePath", file.getAbsolutePath());
                    fileInfo.put("fileSize", formatFileSize(file.length()));
                    fileInfo.put("createTime", sdf.format(new Date(file.lastModified())));
                    fileInfo.put("isCompressed", file.getName().endsWith(".gz"));
                    
                    backupFiles.add(fileInfo);
                }
            }
            
        } catch (Exception e) {
            log.error("获取备份文件列表异常：", e);
        }
        
        return backupFiles;
    }
    
    /**
     * 删除备份文件
     */
    public boolean deleteBackupFile(String fileName) {
        try {
            File file = new File(backupPath + File.separator + fileName);
            if (file.exists() && file.isFile()) {
                boolean deleted = file.delete();
                if (deleted) {
                    log.info("删除备份文件成功：{}", fileName);
                } else {
                    log.error("删除备份文件失败：{}", fileName);
                }
                return deleted;
            } else {
                log.warn("备份文件不存在：{}", fileName);
                return false;
            }
        } catch (Exception e) {
            log.error("删除备份文件异常：", e);
            return false;
        }
    }
    
    /**
     * 获取备份目录磁盘使用情况
     */
    public Map<String, Object> getBackupDiskUsage() {
        Map<String, Object> diskUsage = new HashMap<>();
        
        try {
            File backupDir = new File(backupPath);
            if (backupDir.exists()) {
                long totalSpace = backupDir.getTotalSpace();
                long freeSpace = backupDir.getFreeSpace();
                long usedSpace = totalSpace - freeSpace;
                
                diskUsage.put("totalSpace", formatFileSize(totalSpace));
                diskUsage.put("freeSpace", formatFileSize(freeSpace));
                diskUsage.put("usedSpace", formatFileSize(usedSpace));
                diskUsage.put("usagePercent", Math.round((double) usedSpace / totalSpace * 100));
                
                // 计算备份文件总大小
                long backupFilesSize = calculateBackupFilesSize();
                diskUsage.put("backupFilesSize", formatFileSize(backupFilesSize));
            }
        } catch (Exception e) {
            log.error("获取磁盘使用情况异常：", e);
        }
        
        return diskUsage;
    }
    
    /**
     * 计算备份文件总大小
     */
    private long calculateBackupFilesSize() {
        long totalSize = 0;
        
        try {
            File backupDir = new File(backupPath);
            File[] files = backupDir.listFiles((dir, name) -> 
                name.endsWith(".sql") || name.endsWith(".sql.gz"));
            
            if (files != null) {
                for (File file : files) {
                    totalSize += file.length();
                }
            }
        } catch (Exception e) {
            log.error("计算备份文件大小异常：", e);
        }
        
        return totalSize;
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * 验证备份文件完整性（简单检查）
     */
    public boolean validateBackupFile(String fileName) {
        try {
            File file = new File(backupPath + File.separator + fileName);
            if (!file.exists()) {
                return false;
            }
            
            // 检查文件大小（备份文件不应该为空）
            if (file.length() == 0) {
                log.warn("备份文件为空：{}", fileName);
                return false;
            }
            
            // 对于SQL文件，可以检查是否包含基本的SQL结构
            if (fileName.endsWith(".sql")) {
                // 这里可以添加更详细的SQL文件验证逻辑
                return true;
            }
            
            // 对于压缩文件，检查是否可以正常读取
            if (fileName.endsWith(".gz")) {
                // 这里可以添加压缩文件完整性检查
                return true;
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("验证备份文件异常：", e);
            return false;
        }
    }
    
    /**
     * 获取备份统计信息
     */
    public Map<String, Object> getBackupStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            List<Map<String, Object>> backupFiles = getBackupFileList();
            
            statistics.put("totalFiles", backupFiles.size());
            statistics.put("compressedFiles", backupFiles.stream()
                .mapToInt(file -> (Boolean) file.get("isCompressed") ? 1 : 0)
                .sum());
            
            // 最新备份时间
            if (!backupFiles.isEmpty()) {
                statistics.put("latestBackup", backupFiles.get(0).get("createTime"));
            } else {
                statistics.put("latestBackup", "无备份记录");
            }
            
            // 备份文件总大小
            long totalSize = calculateBackupFilesSize();
            statistics.put("totalSize", formatFileSize(totalSize));
            
        } catch (Exception e) {
            log.error("获取备份统计信息异常：", e);
        }
        
        return statistics;
    }
}
